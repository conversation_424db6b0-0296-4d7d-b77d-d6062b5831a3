# PHPcrmeb 到 Golang+Gin 迁移方案

## 一、技术架构对比分析

### 1.1 当前PHP技术栈
- **框架**: ThinkPHP 6.0.7 + Swoole 4.4+
- **数据库**: MySQL 5.7+ (utf8mb4) + Redis 缓存
- **ORM**: Think-ORM 2.0
- **前端**: Vue CLI + uni-app
- **部署**: Docker + Supervisor

### 1.2 目标Golang技术栈
- **框架**: Gin 1.9+ + Goroutine
- **数据库**: MySQL 8.0+ + Redis 7.0+
- **ORM**: GORM 1.25+
- **前端**: 保持现有Vue+uni-app
- **部署**: Docker + Kubernetes

## 二、迁移优势分析

### 2.1 性能提升
1. **并发处理能力**
   - PHP: Swoole协程 ~10K并发
   - Go: Goroutine ~100K+并发
   - **提升**: 10倍以上并发处理能力

2. **内存使用**
   - PHP: 每请求约8-16MB内存
   - Go: 每Goroutine约2KB内存
   - **优化**: 内存使用降低90%+

3. **响应时间**
   - PHP: 平均50-100ms
   - Go: 平均5-20ms
   - **改善**: 响应时间减少70%+

### 2.2 运维优势
1. **部署简化**
   - 单一二进制文件部署
   - 无需PHP环境依赖
   - 容器镜像体积减少80%

2. **资源消耗**
   - CPU使用率降低50%
   - 内存占用减少70%
   - 服务器成本节省60%

### 2.3 开发效率
1. **类型安全**: 编译时错误检查
2. **工具链**: 丰富的Go生态工具
3. **维护性**: 更好的代码可读性和维护性

## 三、核心模块迁移策略

### 3.1 数据层迁移 (GORM)

#### 用户模型示例
```go
// 用户基础模型
type User struct {
    UID          uint      `gorm:"primaryKey;column:uid" json:"uid"`
    Account      string    `gorm:"column:account;size:32" json:"account"`
    Pwd          string    `gorm:"column:pwd;size:32" json:"-"`
    RealName     string    `gorm:"column:real_name;size:25" json:"real_name"`
    Birthday     time.Time `gorm:"column:birthday" json:"birthday"`
    CardID       string    `gorm:"column:card_id;size:20" json:"card_id"`
    Mark         string    `gorm:"column:mark;size:255" json:"mark"`
    PartnerID    uint      `gorm:"column:partner_id" json:"partner_id"`
    GroupID      uint      `gorm:"column:group_id" json:"group_id"`
    Nickname     string    `gorm:"column:nickname;size:16" json:"nickname"`
    Avatar       string    `gorm:"column:avatar;size:256" json:"avatar"`
    Phone        string    `gorm:"column:phone;size:15" json:"phone"`
    AddTime      time.Time `gorm:"column:add_time" json:"add_time"`
    AddIP        string    `gorm:"column:add_ip;size:16" json:"add_ip"`
    LastTime     time.Time `gorm:"column:last_time" json:"last_time"`
    LastIP       string    `gorm:"column:last_ip;size:16" json:"last_ip"`
    NowMoney     float64   `gorm:"column:now_money;type:decimal(8,2)" json:"now_money"`
    BrokeragePrice float64 `gorm:"column:brokerage_price;type:decimal(8,2)" json:"brokerage_price"`
    Integral     uint      `gorm:"column:integral" json:"integral"`
    SignNum      uint      `gorm:"column:sign_num" json:"sign_num"`
    Status       uint8     `gorm:"column:status;default:1" json:"status"`
    Level        uint      `gorm:"column:level" json:"level"`
    SpreadUID    uint      `gorm:"column:spread_uid" json:"spread_uid"`
    SpreadTime   time.Time `gorm:"column:spread_time" json:"spread_time"`
    UserType     string    `gorm:"column:user_type;size:32" json:"user_type"`
    IsPromoter   uint8     `gorm:"column:is_promoter;default:0" json:"is_promoter"`
    PayCount     uint      `gorm:"column:pay_count" json:"pay_count"`
    SpreadCount  uint      `gorm:"column:spread_count" json:"spread_count"`
    CleanTime    time.Time `gorm:"column:clean_time" json:"clean_time"`
    AddRes       uint8     `gorm:"column:addres;default:1" json:"addres"`
}

func (User) TableName() string {
    return "eb_user"
}
```

#### 商品模型示例
```go
type Product struct {
    ProductID     uint      `gorm:"primaryKey;column:product_id" json:"product_id"`
    MerID         uint      `gorm:"column:mer_id" json:"mer_id"`
    Image         string    `gorm:"column:image;size:128" json:"image"`
    SliderImage   string    `gorm:"column:slider_image;size:2000" json:"slider_image"`
    StoreName     string    `gorm:"column:store_name;size:128" json:"store_name"`
    StoreInfo     string    `gorm:"column:store_info;size:256" json:"store_info"`
    Keyword       string    `gorm:"column:keyword;size:256" json:"keyword"`
    CateID        uint      `gorm:"column:cate_id" json:"cate_id"`
    Price         float64   `gorm:"column:price;type:decimal(8,2)" json:"price"`
    VipPrice      float64   `gorm:"column:vip_price;type:decimal(8,2)" json:"vip_price"`
    OtPrice       float64   `gorm:"column:ot_price;type:decimal(8,2)" json:"ot_price"`
    Postage       float64   `gorm:"column:postage;type:decimal(8,2)" json:"postage"`
    UnitName      string    `gorm:"column:unit_name;size:32" json:"unit_name"`
    Sort          uint      `gorm:"column:sort;default:0" json:"sort"`
    Sales         uint      `gorm:"column:sales;default:0" json:"sales"`
    Stock         uint      `gorm:"column:stock;default:0" json:"stock"`
    IsShow        uint8     `gorm:"column:is_show;default:1" json:"is_show"`
    IsHot         uint8     `gorm:"column:is_hot;default:0" json:"is_hot"`
    IsBenefit     uint8     `gorm:"column:is_benefit;default:0" json:"is_benefit"`
    IsBest        uint8     `gorm:"column:is_best;default:0" json:"is_best"`
    IsNew         uint8     `gorm:"column:is_new;default:0" json:"is_new"`
    AddTime       time.Time `gorm:"column:add_time" json:"add_time"`
    IsPostage     uint8     `gorm:"column:is_postage;default:0" json:"is_postage"`
    IsDel         uint8     `gorm:"column:is_del;default:0" json:"is_del"`
    MerUse        uint8     `gorm:"column:mer_use;default:1" json:"mer_use"`
    GiveIntegral  uint      `gorm:"column:give_integral" json:"give_integral"`
    Cost          float64   `gorm:"column:cost;type:decimal(8,2)" json:"cost"`
    IsSeckill     uint8     `gorm:"column:is_seckill;default:0" json:"is_seckill"`
    IsBargain     uint8     `gorm:"column:is_bargain;default:0" json:"is_bargain"`
    IsGood        uint8     `gorm:"column:is_good;default:0" json:"is_good"`
    FicTi         uint      `gorm:"column:ficti" json:"ficti"`
    Browse        uint      `gorm:"column:browse;default:0" json:"browse"`
    CodePath      string    `gorm:"column:code_path;size:64" json:"code_path"`
    SoureLink     string    `gorm:"column:soure_link;size:255" json:"soure_link"`
    
    // 关联关系
    Merchant      Merchant           `gorm:"foreignKey:MerID" json:"merchant,omitempty"`
    Category      StoreCategory      `gorm:"foreignKey:CateID" json:"category,omitempty"`
    AttrValues    []ProductAttrValue `gorm:"foreignKey:ProductID" json:"attr_values,omitempty"`
}

func (Product) TableName() string {
    return "eb_store_product"
}
```

### 3.2 服务层架构

#### 用户服务示例
```go
type UserService struct {
    userRepo     repository.UserRepository
    billRepo     repository.UserBillRepository
    cache        cache.Cache
    logger       logger.Logger
}

func NewUserService(userRepo repository.UserRepository, billRepo repository.UserBillRepository, cache cache.Cache, logger logger.Logger) *UserService {
    return &UserService{
        userRepo: userRepo,
        billRepo: billRepo,
        cache:    cache,
        logger:   logger,
    }
}

// 用户注册
func (s *UserService) Register(ctx context.Context, req *dto.RegisterRequest) (*dto.UserResponse, error) {
    // 参数验证
    if err := s.validateRegisterRequest(req); err != nil {
        return nil, err
    }
    
    // 检查用户是否已存在
    exists, err := s.userRepo.ExistsByPhone(ctx, req.Phone)
    if err != nil {
        s.logger.Error("检查用户是否存在失败", zap.Error(err))
        return nil, err
    }
    if exists {
        return nil, errors.New("手机号已注册")
    }
    
    // 密码加密
    hashedPwd, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
    if err != nil {
        return nil, err
    }
    
    // 创建用户
    user := &model.User{
        Account:    req.Phone,
        Pwd:        string(hashedPwd),
        Phone:      req.Phone,
        Nickname:   req.Nickname,
        AddTime:    time.Now(),
        AddIP:      req.IP,
        Status:     1,
        SpreadUID:  req.SpreadUID,
    }
    
    if err := s.userRepo.Create(ctx, user); err != nil {
        s.logger.Error("创建用户失败", zap.Error(err))
        return nil, err
    }
    
    // 处理推广关系
    if req.SpreadUID > 0 {
        if err := s.handleSpreadRelation(ctx, user.UID, req.SpreadUID); err != nil {
            s.logger.Error("处理推广关系失败", zap.Error(err))
        }
    }
    
    return s.buildUserResponse(user), nil
}

// 用户登录
func (s *UserService) Login(ctx context.Context, req *dto.LoginRequest) (*dto.LoginResponse, error) {
    user, err := s.userRepo.GetByPhone(ctx, req.Phone)
    if err != nil {
        return nil, errors.New("用户不存在")
    }
    
    if err := bcrypt.CompareHashAndPassword([]byte(user.Pwd), []byte(req.Password)); err != nil {
        return nil, errors.New("密码错误")
    }
    
    // 生成JWT Token
    token, err := s.generateToken(user)
    if err != nil {
        return nil, err
    }
    
    // 更新登录信息
    user.LastTime = time.Now()
    user.LastIP = req.IP
    s.userRepo.Update(ctx, user)
    
    return &dto.LoginResponse{
        Token: token,
        User:  s.buildUserResponse(user),
    }, nil
}
```

### 3.3 API层设计

#### Gin路由配置
```go
func SetupRoutes(r *gin.Engine, services *service.Services) {
    // 中间件
    r.Use(middleware.CORS())
    r.Use(middleware.Logger())
    r.Use(middleware.Recovery())
    r.Use(middleware.RateLimit())
    
    // API版本分组
    v1 := r.Group("/api/v1")
    {
        // 用户相关
        userGroup := v1.Group("/user")
        {
            userGroup.POST("/register", handler.NewUserHandler(services.User).Register)
            userGroup.POST("/login", handler.NewUserHandler(services.User).Login)
            userGroup.GET("/profile", middleware.Auth(), handler.NewUserHandler(services.User).Profile)
            userGroup.PUT("/profile", middleware.Auth(), handler.NewUserHandler(services.User).UpdateProfile)
        }
        
        // 商品相关
        productGroup := v1.Group("/product")
        {
            productGroup.GET("/list", handler.NewProductHandler(services.Product).List)
            productGroup.GET("/:id", handler.NewProductHandler(services.Product).Detail)
            productGroup.GET("/category/:cateId", handler.NewProductHandler(services.Product).ListByCategory)
        }
        
        // 订单相关
        orderGroup := v1.Group("/order")
        orderGroup.Use(middleware.Auth())
        {
            orderGroup.POST("/create", handler.NewOrderHandler(services.Order).Create)
            orderGroup.GET("/list", handler.NewOrderHandler(services.Order).List)
            orderGroup.GET("/:id", handler.NewOrderHandler(services.Order).Detail)
            orderGroup.PUT("/:id/cancel", handler.NewOrderHandler(services.Order).Cancel)
        }
        
        // 支付相关
        payGroup := v1.Group("/pay")
        payGroup.Use(middleware.Auth())
        {
            payGroup.POST("/wechat", handler.NewPayHandler(services.Pay).WechatPay)
            payGroup.POST("/alipay", handler.NewPayHandler(services.Pay).AlipayPay)
            payGroup.POST("/notify/wechat", handler.NewPayHandler(services.Pay).WechatNotify)
            payGroup.POST("/notify/alipay", handler.NewPayHandler(services.Pay).AlipayNotify)
        }
    }
    
    // 商户后台API
    merchant := r.Group("/merchant/api")
    merchant.Use(middleware.MerchantAuth())
    {
        // 商品管理
        merchant.GET("/product/list", handler.NewMerchantProductHandler(services.Product).List)
        merchant.POST("/product", handler.NewMerchantProductHandler(services.Product).Create)
        merchant.PUT("/product/:id", handler.NewMerchantProductHandler(services.Product).Update)
        merchant.DELETE("/product/:id", handler.NewMerchantProductHandler(services.Product).Delete)
        
        // 订单管理
        merchant.GET("/order/list", handler.NewMerchantOrderHandler(services.Order).List)
        merchant.PUT("/order/:id/ship", handler.NewMerchantOrderHandler(services.Order).Ship)
    }
    
    // 平台管理API
    admin := r.Group("/admin/api")
    admin.Use(middleware.AdminAuth())
    {
        // 商户管理
        admin.GET("/merchant/list", handler.NewAdminMerchantHandler(services.Merchant).List)
        admin.POST("/merchant", handler.NewAdminMerchantHandler(services.Merchant).Create)
        admin.PUT("/merchant/:id/status", handler.NewAdminMerchantHandler(services.Merchant).UpdateStatus)
        
        // 系统配置
        admin.GET("/config", handler.NewAdminConfigHandler(services.Config).Get)
        admin.PUT("/config", handler.NewAdminConfigHandler(services.Config).Update)
    }
}
```
